// Copyright Aura Cronos Studios, Inc. All Rights Reserved.

#include "PCG/AURACRONPCGEnergyPulse.h"
#include "Data/AURACRONEnums.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "NiagaraSystem.h"
#include "Components/PointLightComponent.h"
#include "Components/AudioComponent.h"
#include "Components/SphereComponent.h"
#include "Kismet/GameplayStatics.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "GameFramework/PlayerController.h"
#include "Engine/World.h"
#include "Engine/StreamableManager.h"
#include "TimerManager.h"
#include "Logging/StructuredLog.h"
#include "Net/UnrealNetwork.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "AbilitySystemComponent.h"
#include "AbilitySystemInterface.h"
#include "GAS/AURACRONAttributeSet.h"
#include "GameplayEffect.h"
#include "GameplayEffectTypes.h"
#include "Character/AURACRONCharacter.h"

// Sets default values
AAURACRONPCGEnergyPulse::AAURACRONPCGEnergyPulse()
{
    // Set this actor to call Tick() every frame - otimizado com timer
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.TickInterval = 0.1f; // Otimização: tick a cada 0.1s ao invés de todo frame

    // Habilitar replicação para multiplayer
    bReplicates = true;
    bAlwaysRelevant = true;
    NetUpdateFrequency = 30.0f; // 30 updates por segundo para multiplayer

    // Criar componente raiz
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));

    // Criar componente de partículas Niagara
    PulseEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("PulseEffect"));
    PulseEffect->SetupAttachment(RootComponent);
    PulseEffect->SetAutoActivate(false); // Não ativar automaticamente

    // Criar componente de luz
    PulseLight = CreateDefaultSubobject<UPointLightComponent>(TEXT("PulseLight"));
    PulseLight->SetupAttachment(RootComponent);
    PulseLight->SetLightColor(FLinearColor::White);
    PulseLight->SetIntensity(5000.0f);
    PulseLight->SetAttenuationRadius(2000.0f);
    PulseLight->SetCastShadows(false);
    PulseLight->SetVisibility(false); // Inicialmente invisível

    // Criar componente de áudio
    PulseSound = CreateDefaultSubobject<UAudioComponent>(TEXT("PulseSound"));
    PulseSound->SetupAttachment(RootComponent);
    PulseSound->bAutoActivate = false;

    // Criar componente de colisão esférica
    PulseSphere = CreateDefaultSubobject<USphereComponent>(TEXT("PulseSphere"));
    PulseSphere->SetupAttachment(RootComponent);
    PulseSphere->SetSphereRadius(100.0f); // Raio inicial pequeno
    PulseSphere->SetCollisionProfileName(TEXT("OverlapAllDynamic"));
    PulseSphere->OnComponentBeginOverlap.AddDynamic(this, &AAURACRONPCGEnergyPulse::OnPlayerEnterPulseRadius);

    // Valores padrão alinhados com documentação AURACRON
    PulseRadius = 10000.0f;
    PulseDuration = 5.0f;
    PulseIntensity = 1.0f;
    PulseColor = FLinearColor(0.0f, 0.8f, 1.0f, 1.0f); // Azul ciano
    ExpansionSpeed = 1.0f;
    QualityScale = 1.0f;
    CurrentMapPhase = EAURACRONMapPhase::Awakening;
    ElapsedTime = 0.0f;
    bPulseActive = false;
    CurrentRadius = 0.0f;
    BaseDamage = 10.0f;
    EnergyType = EAURACRONEnergyType::Golden; // Tipo de energia padrão

    // Inicializar StreamableManager para carregamento assíncrono UE 5.6
    StreamableManager = &UAssetManager::GetStreamableManager();

    // Inicializar orçamento de partículas escalável baseado na documentação
    ParticleBudget = 1000; // Padrão para dispositivos mid-range

    // Inicializar sistema de pooling de efeitos
    EffectPoolSize = 10;

    // Inicializar integração com Trilhos dinâmicos
    bAffectedBySolarTrilhos = true;
    bAffectedByAxisTrilhos = true;
    bAffectedByLunarTrilhos = true;

    // Inicializar sistema de territorialidade
    ControllingTeam = 0; // Neutro
    TerritorialInfluence = 1.0f;
}

// Called when the game starts or when spawned
void AAURACRONPCGEnergyPulse::BeginPlay()
{
    Super::BeginPlay();

    // Desativar efeitos inicialmente
    if (PulseEffect && IsValid(PulseEffect))
    {
        PulseEffect->Deactivate();
    }

    if (PulseLight && IsValid(PulseLight))
    {
        PulseLight->SetVisibility(false);
    }

    // Inicializar carregamento assíncrono de assets Niagara para UE 5.6
    LoadNiagaraAssetsAsync();

    // Configurar orçamento de partículas baseado na capacidade do dispositivo
    ConfigureParticleBudgetForDevice();

    // Inicializar sistema de pooling de efeitos
    InitializeEffectPooling();

    // Configurar integração com GameplayAbilitySystem se disponível
    InitializeAbilitySystemIntegration();

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGEnergyPulse: Inicializado com tipo de energia {0} e orçamento de partículas {1}",
              StaticEnum<EAURACRONEnergyType>()->GetNameStringByValue((int64)EnergyType), ParticleBudget);
}

// Called every frame - otimizado para performance
void AAURACRONPCGEnergyPulse::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Processar pulso ativo apenas se necessário
    if (bPulseActive && IsValid(this))
    {
        // Atualizar tempo decorrido
        ElapsedTime += DeltaTime * PulseSpeed;

        // Verificar se o pulso terminou
        if (ElapsedTime >= PulseDuration)
        {
            // Desativar pulso de forma robusta
            DeactivatePulse();

            UE_LOGFMT(LogTemp, Verbose, "AAURACRONPCGEnergyPulse: Pulso concluído após {0} segundos", ElapsedTime);
            return;
        }

        // Atualizar efeitos visuais com validações robustas
        UpdateVisualEffects();

        // Aplicar efeitos aos jogadores e ambiente com validações
        ApplyEffectsToPlayers();
        ApplyEffectsToEnvironment();

        // Atualizar integração com Trilhos dinâmicos
        UpdateTrilhosIntegration();

        // Atualizar sistema de territorialidade
        UpdateTerritorialControl();

        // Replicar estado para multiplayer se necessário
        if (HasAuthority())
        {
            ReplicatePulseState();
        }
    }
}

void AAURACRONPCGEnergyPulse::TriggerPulse(float Duration, float Intensity)
{
    // Validações robustas
    if (!IsValid(this) || !GetWorld())
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGEnergyPulse::TriggerPulse: Objeto ou World inválido");
        return;
    }

    // Configurar parâmetros do pulso com validações
    PulseDuration = Duration > 0.0f ? Duration : PulseDuration;
    PulseIntensity = FMath::Clamp(Intensity, 0.1f, 5.0f); // Limitar intensidade

    // Reiniciar tempo e ativar pulso
    ElapsedTime = 0.0f;
    bPulseActive = true;
    CurrentRadius = 0.0f;

    // Ativar efeitos visuais com validações robustas
    if (PulseEffect && IsValid(PulseEffect))
    {
        PulseEffect->Activate(true);
    }

    if (PulseLight && IsValid(PulseLight))
    {
        PulseLight->SetVisibility(true);
    }

    if (PulseSound && IsValid(PulseSound))
    {
        PulseSound->Play();
    }

    // Atualizar efeitos visuais iniciais
    UpdateVisualEffects();

    // Aplicar GameplayEffect se integrado com AbilitySystem
    ApplyGameplayEffectToNearbyPlayers();

    // Notificar sistema de territorialidade
    NotifyTerritorialSystem();

    // Replicar para multiplayer
    if (HasAuthority())
    {
        MulticastTriggerPulse(Duration, Intensity);
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGEnergyPulse: Pulso iniciado com intensidade {0} e duração {1} para tipo de energia {2}",
              PulseIntensity, PulseDuration, StaticEnum<EAURACRONEnergyType>()->GetNameStringByValue((int64)EnergyType));
}

void AAURACRONPCGEnergyPulse::CreateGoldenEnergyPulse(float Duration, float Intensity)
{
    // Validações robustas
    if (!IsValid(this))
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGEnergyPulse::CreateGoldenEnergyPulse: Objeto inválido");
        return;
    }

    // Configurar tipo de energia alinhado com documentação AURACRON
    EnergyType = EAURACRONEnergyType::Golden;

    // Configurar cor dourada para portais radiantes (baseado na documentação)
    PulseColor = FLinearColor(1.0f, 0.84f, 0.0f, 1.0f); // Dourado conforme documentação

    // Configurar parâmetros específicos para energia dourada
    PulseSpeed = 1.2f;

    // Configurar luz para energia dourada com validações
    if (PulseLight && IsValid(PulseLight))
    {
        PulseLight->SetLightColor(PulseColor);
        PulseLight->SetIntensity(6000.0f * QualityScale); // Aplicar escala de qualidade
        PulseLight->SetAttenuationRadius(2500.0f); // Raio maior para energia dourada
    }

    // Configurar efeitos Niagara específicos para energia dourada
    if (PulseEffect && IsValid(PulseEffect))
    {
        PulseEffect->SetVectorParameter(FName("EnergyColor"), FVector(PulseColor.R, PulseColor.G, PulseColor.B));
        PulseEffect->SetFloatParameter(FName("GoldenIntensity"), 1.5f);
        PulseEffect->SetFloatParameter(FName("ParticleSize"), 1.2f);
    }

    // Aplicar efeitos específicos de energia dourada (regeneração/cura)
    GoldenEnergyBuffDuration = Duration * 1.5f; // Buff dura mais que o pulso

    // Disparar pulso
    TriggerPulse(Duration, Intensity);

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGEnergyPulse: Pulso de energia dourada criado com duração {0} e intensidade {1}", Duration, Intensity);
}

void AAURACRONPCGEnergyPulse::CreateSilverEnergyPulse(float Duration, float Intensity)
{
    // Validações robustas
    if (!IsValid(this))
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGEnergyPulse::CreateSilverEnergyPulse: Objeto inválido");
        return;
    }

    // Configurar tipo de energia alinhado com documentação AURACRON
    EnergyType = EAURACRONEnergyType::Silver;

    // Configurar cor prateada para portais zephyr (baseado na documentação)
    PulseColor = FLinearColor(0.75f, 0.75f, 0.8f, 1.0f); // Prateado conforme documentação

    // Configurar parâmetros específicos para energia prateada (Firmamento Zephyr)
    PulseSpeed = 1.5f; // Mais rápida que dourada

    // Configurar luz para energia prateada com validações
    if (PulseLight && IsValid(PulseLight))
    {
        PulseLight->SetLightColor(PulseColor);
        PulseLight->SetIntensity(5000.0f * QualityScale); // Aplicar escala de qualidade
        PulseLight->SetAttenuationRadius(2200.0f); // Raio específico para energia prateada
    }

    // Configurar efeitos Niagara específicos para energia prateada
    if (PulseEffect && IsValid(PulseEffect))
    {
        PulseEffect->SetVectorParameter(FName("EnergyColor"), FVector(PulseColor.R, PulseColor.G, PulseColor.B));
        PulseEffect->SetFloatParameter(FName("SilverIntensity"), 1.3f);
        PulseEffect->SetFloatParameter(FName("WindEffect"), 2.0f); // Efeito de vento para Zephyr
        PulseEffect->SetFloatParameter(FName("ParticleSpeed"), 2.0f); // Partículas mais rápidas
    }

    // Aplicar efeitos específicos de energia prateada (velocidade/mobilidade)
    SilverEnergyBuffDuration = Duration * 1.2f;

    // Disparar pulso
    TriggerPulse(Duration, Intensity);

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGEnergyPulse: Pulso de energia prateada criado com duração {0} e intensidade {1}", Duration, Intensity);
}

void AAURACRONPCGEnergyPulse::CreateVioletEnergyPulse(float Duration, float Intensity)
{
    // Validações robustas
    if (!IsValid(this))
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGEnergyPulse::CreateVioletEnergyPulse: Objeto inválido");
        return;
    }

    // Configurar tipo de energia alinhado com documentação AURACRON
    EnergyType = EAURACRONEnergyType::Violet;

    // Configurar cor violeta para portais umbrais (baseado na documentação)
    PulseColor = FLinearColor(0.5f, 0.0f, 1.0f, 1.0f); // Violeta conforme documentação

    // Configurar parâmetros específicos para energia violeta (Reino Purgatório)
    PulseSpeed = 1.8f; // Mais rápida que prateada

    // Configurar luz para energia violeta com validações
    if (PulseLight && IsValid(PulseLight))
    {
        PulseLight->SetLightColor(PulseColor);
        PulseLight->SetIntensity(7000.0f * QualityScale); // Aplicar escala de qualidade
        PulseLight->SetAttenuationRadius(2800.0f); // Raio maior para energia violeta
    }

    // Configurar efeitos Niagara específicos para energia violeta
    if (PulseEffect && IsValid(PulseEffect))
    {
        PulseEffect->SetVectorParameter(FName("EnergyColor"), FVector(PulseColor.R, PulseColor.G, PulseColor.B));
        PulseEffect->SetFloatParameter(FName("VioletIntensity"), 1.8f);
        PulseEffect->SetFloatParameter(FName("SpectralEffect"), 2.5f); // Efeito espectral para Purgatório
        PulseEffect->SetFloatParameter(FName("DistortionStrength"), 1.5f); // Distorção dimensional
    }

    // Aplicar efeitos específicos de energia violeta (furtividade/fase)
    VioletEnergyBuffDuration = Duration * 2.0f; // Buff dura mais tempo

    // Disparar pulso
    TriggerPulse(Duration, Intensity);

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGEnergyPulse: Pulso de energia violeta criado com duração {0} e intensidade {1}", Duration, Intensity);
}

// ========================================
// IMPLEMENTAÇÃO DOS TIPOS DE ENERGIA FALTANTES - SOLAR E LUNAR
// ========================================

void AAURACRONPCGEnergyPulse::CreateSolarEnergyPulse(float Duration, float Intensity)
{
    // Validações robustas
    if (!IsValid(this))
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGEnergyPulse::CreateSolarEnergyPulse: Objeto inválido");
        return;
    }

    // Configurar tipo de energia Solar (baseado na documentação dos Trilhos)
    EnergyType = EAURACRONEnergyType::Solar;

    // Configurar cor solar dourada intensa
    PulseColor = FLinearColor(1.0f, 0.9f, 0.2f, 1.0f); // Dourado solar mais intenso

    // Configurar parâmetros específicos para energia solar
    PulseSpeed = 1.0f; // Velocidade baseada na posição do sol

    // Configurar luz para energia solar com validações
    if (PulseLight && IsValid(PulseLight))
    {
        PulseLight->SetLightColor(PulseColor);
        PulseLight->SetIntensity(8000.0f * QualityScale); // Mais intensa que dourada
        PulseLight->SetAttenuationRadius(3000.0f); // Raio maior para energia solar
        PulseLight->SetCastShadows(true); // Solar pode projetar sombras
    }

    // Configurar efeitos Niagara específicos para energia solar
    if (PulseEffect && IsValid(PulseEffect))
    {
        PulseEffect->SetVectorParameter(FName("EnergyColor"), FVector(PulseColor.R, PulseColor.G, PulseColor.B));
        PulseEffect->SetFloatParameter(FName("SolarIntensity"), 2.0f);
        PulseEffect->SetFloatParameter(FName("HeatDistortion"), 1.8f);
        PulseEffect->SetFloatParameter(FName("LightRays"), 3.0f);
    }

    // Aplicar efeitos específicos de energia solar (regeneração + velocidade)
    SolarEnergyBuffDuration = Duration * 1.8f;

    // Disparar pulso
    TriggerPulse(Duration, Intensity);

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGEnergyPulse: Pulso de energia solar criado com duração {0} e intensidade {1}", Duration, Intensity);
}

void AAURACRONPCGEnergyPulse::CreateLunarEnergyPulse(float Duration, float Intensity)
{
    // Validações robustas
    if (!IsValid(this))
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGEnergyPulse::CreateLunarEnergyPulse: Objeto inválido");
        return;
    }

    // Configurar tipo de energia Lunar (baseado na documentação dos Trilhos)
    EnergyType = EAURACRONEnergyType::Lunar;

    // Configurar cor lunar azul-branco etérea
    PulseColor = FLinearColor(0.7f, 0.8f, 1.0f, 0.9f); // Azul-branco lunar

    // Configurar parâmetros específicos para energia lunar
    PulseSpeed = 0.8f; // Mais lenta, etérea

    // Configurar luz para energia lunar com validações
    if (PulseLight && IsValid(PulseLight))
    {
        PulseLight->SetLightColor(PulseColor);
        PulseLight->SetIntensity(4000.0f * QualityScale); // Mais suave que solar
        PulseLight->SetAttenuationRadius(3500.0f); // Raio maior mas mais suave
        PulseLight->SetCastShadows(false); // Lunar não projeta sombras
    }

    // Configurar efeitos Niagara específicos para energia lunar
    if (PulseEffect && IsValid(PulseEffect))
    {
        PulseEffect->SetVectorParameter(FName("EnergyColor"), FVector(PulseColor.R, PulseColor.G, PulseColor.B));
        PulseEffect->SetFloatParameter(FName("LunarIntensity"), 1.5f);
        PulseEffect->SetFloatParameter(FName("EtherealEffect"), 2.2f);
        PulseEffect->SetFloatParameter(FName("StarDust"), 2.8f);
        PulseEffect->SetFloatParameter(FName("PhaseShift"), 1.3f);
    }

    // Aplicar efeitos específicos de energia lunar (furtividade + visão)
    LunarEnergyBuffDuration = Duration * 2.2f; // Buff dura mais tempo

    // Disparar pulso
    TriggerPulse(Duration, Intensity);

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGEnergyPulse: Pulso de energia lunar criado com duração {0} e intensidade {1}", Duration, Intensity);
}

void AAURACRONPCGEnergyPulse::CreateEnergyPulseForPortalType(EAURACRONPortalType PortalType, float Duration, float Intensity)
{
    // Criar pulso baseado no tipo de portal
    switch (PortalType)
    {
    case EAURACRONPortalType::RadiantPlains:
        CreateGoldenEnergyPulse(Duration, Intensity);
        break;
        
    case EAURACRONPortalType::ZephyrFirmament:
        CreateSilverEnergyPulse(Duration, Intensity);
        break;
        
    case EAURACRONPortalType::PurgatoryRealm:
        CreateVioletEnergyPulse(Duration, Intensity);
        break;
        
    default:
        // Usar pulso padrão
        TriggerPulse(Duration, Intensity);
        break;
    }
}

void AAURACRONPCGEnergyPulse::SetQualityScale(float NewQualityScale)
{
    QualityScale = FMath::Clamp(NewQualityScale, 0.1f, 1.0f);
    
    // Ajustar qualidade dos efeitos visuais
    if (PulseEffect)
    {
        // Ajustar densidade de partículas baseado na escala de qualidade
        PulseEffect->SetFloatParameter(FName("ParticleDensity"), QualityScale);
        
        // Ajustar qualidade de iluminação
        PulseLight->SetIntensity(5000.0f * QualityScale);
    }
    
    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnergyPulse: Escala de qualidade ajustada para %.2f"), QualityScale);
}

void AAURACRONPCGEnergyPulse::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    // Validações robustas
    if (!IsValid(this))
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGEnergyPulse::UpdateForMapPhase: Objeto inválido");
        return;
    }

    CurrentMapPhase = MapPhase;

    // Ajustar parâmetros baseados na fase do mapa (alinhado com documentação AURACRON)
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening: // FASE 1: DESPERTAR (0-15 min)
            PulseColor = FLinearColor(0.0f, 0.8f, 1.0f, 1.0f); // Azul ciano
            PulseDuration = 5.0f;
            PulseIntensity = 0.5f;
            ParticleBudget = 500; // Orçamento reduzido para fase inicial
            break;

        case EAURACRONMapPhase::Expansion: // FASE INTERMEDIÁRIA (15-20 min)
            PulseColor = FLinearColor(0.0f, 1.0f, 0.5f, 1.0f); // Verde esmeralda
            PulseDuration = 4.5f;
            PulseIntensity = 0.75f;
            ParticleBudget = 800; // Orçamento aumentado
            break;

        case EAURACRONMapPhase::Convergence: // FASE 2: CONVERGÊNCIA (15-25 min)
            PulseColor = FLinearColor(1.0f, 0.5f, 0.0f, 1.0f); // Laranja
            PulseDuration = 4.0f;
            PulseIntensity = 1.0f;
            ParticleBudget = 1200; // Orçamento moderado
            break;

        case EAURACRONMapPhase::Intensification: // FASE 3: INTENSIFICAÇÃO (25-35 min) - FALTAVA!
            PulseColor = FLinearColor(1.0f, 0.2f, 0.8f, 1.0f); // Rosa intenso
            PulseDuration = 3.8f;
            PulseIntensity = 1.3f;
            ParticleBudget = 1800; // Orçamento alto para intensificação
            break;

        case EAURACRONMapPhase::Resolution: // FASE 4: RESOLUÇÃO (35+ min)
            PulseColor = FLinearColor(1.0f, 0.0f, 0.5f, 1.0f); // Magenta
            PulseDuration = 3.5f;
            PulseIntensity = 1.5f;
            ParticleBudget = 2500; // Orçamento máximo para fase final
            break;

        default:
            UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGEnergyPulse: Fase do mapa desconhecida: {0}", (int32)CurrentMapPhase);
            break;
    }

    // Atualizar efeitos visuais baseados na nova fase
    UpdateVisualEffectsForPhase();

    // Atualizar orçamento de partículas baseado na fase
    ConfigureParticleBudgetForPhase();

    // Notificar sistema de territorialidade sobre mudança de fase
    NotifyPhaseChange();

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGEnergyPulse: Atualizado para fase {0} com orçamento de partículas {1}",
              StaticEnum<EAURACRONMapPhase>()->GetNameStringByValue((int64)CurrentMapPhase), ParticleBudget);
}

void AAURACRONPCGEnergyPulse::UpdateVisualEffects()
{
    // Calcular raio atual do pulso
    CurrentRadius = CalculateCurrentRadius();
    
    // Calcular progresso normalizado (0.0 a 1.0)
    float NormalizedProgress = ElapsedTime / PulseDuration;
    
    // Calcular alpha para fade in/out
    float Alpha = 1.0f;
    if (NormalizedProgress < 0.2f) // Fade in
    {
        Alpha = NormalizedProgress / 0.2f;
    }
    else if (NormalizedProgress > 0.8f) // Fade out
    {
        Alpha = (1.0f - NormalizedProgress) / 0.2f;
    }
    
    // Atualizar parâmetros do sistema de partículas
    if (PulseEffect)
    {
        PulseEffect->SetFloatParameter(FName("Radius"), CurrentRadius);
        PulseEffect->SetFloatParameter(FName("Intensity"), PulseIntensity * Alpha);
        PulseEffect->SetColorParameter(FName("Color"), PulseColor);
    }
    
    // Atualizar luz
    if (PulseLight)
    {
        PulseLight->SetLightColor(PulseColor);
        PulseLight->SetIntensity(5000.0f * PulseIntensity * Alpha * QualityScale);
        PulseLight->SetAttenuationRadius(CurrentRadius * 0.5f);
    }
    
    // Atualizar esfera de colisão
    if (PulseSphere)
    {
        PulseSphere->SetSphereRadius(CurrentRadius, true);
    }
}

void AAURACRONPCGEnergyPulse::ApplyPulseEffects()
{
    // Verificar posições dos jogadores
    CheckPlayerPositions();
    
    // Aplicar efeitos ao ambiente dentro do raio
    // Implementação específica depende do design do jogo
    
    // Exemplo: Afetar materiais dinâmicos no ambiente
    if (CurrentMapPhase == EAURACRONMapPhase::Resolution)
    {
        // Obter todos os atores de ambiente afetáveis
        TArray<AActor*> EnvironmentActors;
        UGameplayStatics::GetAllActorsWithTag(GetWorld(), FName("AffectedByEnergyPulse"), EnvironmentActors);
        
        for (AActor* EnvActor : EnvironmentActors)
        {
            if (!EnvActor)
            {
                continue;
            }
            
            // Calcular distância do ator ao centro do pulso
            float Distance = FVector::Dist(EnvActor->GetActorLocation(), GetActorLocation());
            
            // Verificar se o ator está dentro do raio do pulso
            if (Distance <= CurrentRadius)
            {
                // Calcular intensidade baseada na distância
                float DistanceFactor = 1.0f - (Distance / CurrentRadius);
                float EffectIntensity = PulseIntensity * DistanceFactor;
                
                // Aplicar efeito ao ator de ambiente de forma robusta
                // Verificar se o ator implementa interface específica do projeto
                if (EnvActor->GetClass()->ImplementsInterface(UAbilitySystemInterface::StaticClass()))
                {
                    // Aplicar GameplayEffect ao ator com AbilitySystem
                    if (AAURACRONCharacter* AuraCharacter = Cast<AAURACRONCharacter>(EnvActor))
                    {
                        ApplyEnvironmentalGameplayEffect(AuraCharacter, EffectIntensity);
                    }
                }
                else
                {
                    // Aplicar efeito direto para atores sem AbilitySystem
                    ApplyDirectEnvironmentalEffect(EnvActor, EffectIntensity);
                }

                UE_LOGFMT(LogTemp, Verbose, "Energy Pulse: Aplicando efeito ambiental ao ator {0} com intensidade {1}",
                          EnvActor->GetName(), EffectIntensity);
            }
        }
    }
}

void AAURACRONPCGEnergyPulse::CheckPlayerPositions()
{
    // Obter todos os jogadores
    TArray<AActor*> Players;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), ACharacter::StaticClass(), Players);
    
    // Aplicar efeitos aos jogadores dentro do raio
    for (AActor* Player : Players)
    {
        if (!Player)
        {
            continue;
        }
        
        // Calcular distância do jogador ao centro do pulso
        float Distance = FVector::Dist(Player->GetActorLocation(), GetActorLocation());
        
        // Verificar se o jogador está dentro do raio do pulso
        if (Distance <= CurrentRadius)
        {
            // Calcular intensidade baseada na distância (mais forte no centro do pulso)
            float DistanceFactor = 1.0f - (Distance / CurrentRadius);
            float EffectIntensity = PulseIntensity * DistanceFactor;
            
            // Aplicar efeito ao jogador
            float DamageAmount = BaseDamage * EffectIntensity;
            ApplyDamageToPlayer(Player, DamageAmount);
            
            // Exemplo: Aplicar impulso na direção oposta ao centro do pulso
            if (CurrentMapPhase == EAURACRONMapPhase::Resolution && EffectIntensity > 0.5f)
            {
                FVector Direction = (Player->GetActorLocation() - GetActorLocation()).GetSafeNormal();
                float ImpulseStrength = 500.0f * EffectIntensity;
                
                // Aplicar impulso ao personagem
                ACharacter* Character = Cast<ACharacter>(Player);
                if (Character && Character->GetCharacterMovement())
                {
                    Character->GetCharacterMovement()->AddImpulse(Direction * ImpulseStrength);
                }
            }
        }
    }
}

float AAURACRONPCGEnergyPulse::CalculateCurrentRadius()
{
    // Calcular progresso normalizado (0.0 a 1.0)
    float NormalizedProgress = ElapsedTime / PulseDuration;
    
    // Função de expansão não-linear (mais rápida no início, mais lenta no final)
    float ExpansionFactor = FMath::Sqrt(NormalizedProgress);
    
    // Calcular raio atual
    return PulseRadius * ExpansionFactor;
}

float AAURACRONPCGEnergyPulse::CalculateDamageMultiplier(float Distance)
{
    // Quanto mais próximo do centro, maior o dano
    float DistanceFactor = 1.0f - (Distance / CurrentRadius);
    
    // Aplicar curva não-linear para aumentar dano no centro
    return FMath::Pow(DistanceFactor, 2.0f);
}

void AAURACRONPCGEnergyPulse::ApplyDamageToPlayer(AActor* Player, float DamageAmount)
{
    // Aplicar dano ao jogador usando o sistema de dano do Unreal
    if (Player && DamageAmount > 0.0f)
    {
        // Usar o sistema de dano do Unreal
        UGameplayStatics::ApplyDamage(
            Player,                  // Ator alvo
            DamageAmount,            // Quantidade de dano
            nullptr,                 // Instigador (opcional)
            this,                    // Causador do dano
            nullptr                  // Tipo de dano (opcional)
        );
    }
}

void AAURACRONPCGEnergyPulse::OnPlayerEnterPulseRadius(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, 
                                UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, 
                                bool bFromSweep, const FHitResult& SweepResult)
{
    // Verificar se o ator que entrou é um jogador
    ACharacter* Character = Cast<ACharacter>(OtherActor);
    if (Character && bPulseActive)
    {
        // Calcular distância do jogador ao centro do pulso
        float Distance = FVector::Dist(Character->GetActorLocation(), GetActorLocation());
        
        // Calcular multiplicador de dano baseado na distância
        float DamageMultiplier = CalculateDamageMultiplier(Distance);
        
        // Aplicar dano ao jogador
        float DamageAmount = BaseDamage * DamageMultiplier * PulseIntensity;
        ApplyDamageToPlayer(Character, DamageAmount);
        
        // Efeito visual de feedback
        if (PulseEffect)
        {
            // Criar efeito de impacto no jogador
            UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                GetWorld(),
                PulseEffect->GetAsset(),
                Character->GetActorLocation(),
                FRotator::ZeroRotator,
                FVector(0.5f),
                true,
                true,
                ENCPoolMethod::AutoRelease
            );
        }
    }
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES QUE ESTAVAM FALTANDO - UE 5.6 MODERN APIS
// ========================================

void AAURACRONPCGEnergyPulse::ApplyEffectsToPlayers()
{
    // Implementação robusta para aplicar efeitos aos jogadores
    if (!bPulseActive)
    {
        return;
    }

    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    // Buscar todos os jogadores no mundo
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(World, ACharacter::StaticClass(), FoundActors);

    for (AActor* Actor : FoundActors)
    {
        ACharacter* Character = Cast<ACharacter>(Actor);
        if (!Character || !IsValid(Character))
        {
            continue;
        }

        // Verificar se o jogador está dentro do raio do pulso
        float Distance = FVector::Dist(GetActorLocation(), Character->GetActorLocation());
        if (Distance <= CurrentRadius)
        {
            // Aplicar efeitos baseados no tipo de energia
            switch (EnergyType)
            {
                case EAURACRONEnergyType::Golden:
                    ApplyGoldenEnergyEffects(Character);
                    break;

                case EAURACRONEnergyType::Chaos:
                    ApplyChaosEnergyEffects(Character);
                    break;

                case EAURACRONEnergyType::Void:
                    ApplyVoidEnergyEffects(Character);
                    break;

                default:
                    ApplyGenericEnergyEffects(Character);
                    break;
            }

            UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnergyPulse::ApplyEffectsToPlayers - Applied effects to %s"),
                   *Character->GetName());
        }
    }
}

void AAURACRONPCGEnergyPulse::ApplyEffectsToEnvironment()
{
    // Implementação robusta para aplicar efeitos ao ambiente
    if (!bPulseActive)
    {
        return;
    }

    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    // Aplicar efeitos visuais ao ambiente baseado no tipo de energia
    switch (EnergyType)
    {
        case EAURACRONEnergyType::Golden:
            ApplyGoldenEnvironmentEffects();
            break;

        case EAURACRONEnergyType::Chaos:
            ApplyChaosEnvironmentEffects();
            break;

        case EAURACRONEnergyType::Void:
            ApplyVoidEnvironmentEffects();
            break;

        default:
            ApplyGenericEnvironmentEffects();
            break;
    }

    // Aplicar efeitos de iluminação dinâmica
    if (PulseLight && IsValid(PulseLight))
    {
        float IntensityMultiplier = FMath::Lerp(0.5f, 2.0f, PulseIntensity);
        float BaseIntensity = 5000.0f;
        PulseLight->SetIntensity(BaseIntensity * IntensityMultiplier);
        PulseLight->SetAttenuationRadius(CurrentRadius * 1.5f);
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnergyPulse::ApplyEffectsToEnvironment - Applied environment effects"));
}

// ========================================
// FUNÇÕES AUXILIARES PARA EFEITOS ESPECÍFICOS
// ========================================

void AAURACRONPCGEnergyPulse::ApplyGoldenEnergyEffects(ACharacter* Character)
{
    // Implementação robusta para efeitos de energia dourada
    if (!Character || !IsValid(Character))
    {
        return;
    }

    // Aplicar efeito de cura/regeneração
    // Implementar quando sistema de atributos estiver disponível

    // Aplicar efeito visual dourado no jogador
    if (UNiagaraComponent* PlayerEffect = UNiagaraFunctionLibrary::SpawnSystemAttached(
        nullptr, // Sistema Niagara será definido posteriormente
        Character->GetRootComponent(),
        NAME_None,
        FVector::ZeroVector,
        FRotator::ZeroRotator,
        EAttachLocation::KeepWorldPosition,
        true,
        true,
        ENCPoolMethod::AutoRelease))
    {
        PlayerEffect->SetVectorParameter(TEXT("EnergyColor"), FVector(1.0f, 0.84f, 0.0f)); // Dourado
        PlayerEffect->SetFloatParameter(TEXT("Intensity"), PulseIntensity);
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnergyPulse::ApplyGoldenEnergyEffects - Applied to %s"), *Character->GetName());
}

void AAURACRONPCGEnergyPulse::ApplyChaosEnergyEffects(ACharacter* Character)
{
    // Implementação robusta para efeitos de energia do caos
    if (!Character || !IsValid(Character))
    {
        return;
    }

    // Aplicar efeito de dano/debuff caótico
    // Implementar quando sistema de atributos estiver disponível

    // Aplicar efeito visual caótico no jogador
    if (UNiagaraComponent* PlayerEffect = UNiagaraFunctionLibrary::SpawnSystemAttached(
        nullptr, // Sistema Niagara será definido posteriormente
        Character->GetRootComponent(),
        NAME_None,
        FVector::ZeroVector,
        FRotator::ZeroRotator,
        EAttachLocation::KeepWorldPosition,
        true,
        true,
        ENCPoolMethod::AutoRelease))
    {
        PlayerEffect->SetVectorParameter(TEXT("EnergyColor"), FVector(0.8f, 0.1f, 0.1f)); // Vermelho caótico
        PlayerEffect->SetFloatParameter(TEXT("Intensity"), PulseIntensity);
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnergyPulse::ApplyChaosEnergyEffects - Applied to %s"), *Character->GetName());
}

void AAURACRONPCGEnergyPulse::ApplyVoidEnergyEffects(ACharacter* Character)
{
    // Implementação robusta para efeitos de energia do vazio
    if (!Character || !IsValid(Character))
    {
        return;
    }

    // Aplicar efeito de invisibilidade/fase do vazio
    // Implementar quando sistema de atributos estiver disponível

    // Aplicar efeito visual do vazio no jogador
    if (UNiagaraComponent* PlayerEffect = UNiagaraFunctionLibrary::SpawnSystemAttached(
        nullptr, // Sistema Niagara será definido posteriormente
        Character->GetRootComponent(),
        NAME_None,
        FVector::ZeroVector,
        FRotator::ZeroRotator,
        EAttachLocation::KeepWorldPosition,
        true,
        true,
        ENCPoolMethod::AutoRelease))
    {
        PlayerEffect->SetVectorParameter(TEXT("EnergyColor"), FVector(0.5f, 0.3f, 0.9f)); // Roxo do vazio
        PlayerEffect->SetFloatParameter(TEXT("Intensity"), PulseIntensity);
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnergyPulse::ApplyVoidEnergyEffects - Applied to %s"), *Character->GetName());
}

void AAURACRONPCGEnergyPulse::ApplyGenericEnergyEffects(ACharacter* Character)
{
    // Implementação robusta para efeitos de energia genérica
    if (!Character || !IsValid(Character))
    {
        return;
    }

    // Aplicar efeito genérico
    // Implementar quando sistema de atributos estiver disponível

    // Aplicar efeito visual genérico no jogador
    if (UNiagaraComponent* PlayerEffect = UNiagaraFunctionLibrary::SpawnSystemAttached(
        nullptr, // Sistema Niagara será definido posteriormente
        Character->GetRootComponent(),
        NAME_None,
        FVector::ZeroVector,
        FRotator::ZeroRotator,
        EAttachLocation::KeepWorldPosition,
        true,
        true,
        ENCPoolMethod::AutoRelease))
    {
        PlayerEffect->SetVectorParameter(TEXT("EnergyColor"), FVector(1.0f, 1.0f, 1.0f)); // Branco
        PlayerEffect->SetFloatParameter(TEXT("Intensity"), PulseIntensity);
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnergyPulse::ApplyGenericEnergyEffects - Applied to %s"), *Character->GetName());
}

// ========================================
// FUNÇÕES DE EFEITOS AMBIENTAIS
// ========================================

void AAURACRONPCGEnergyPulse::ApplyGoldenEnvironmentEffects()
{
    // Implementação robusta para efeitos ambientais dourados

    // Spawn efeitos dourados no ambiente
    if (UNiagaraSystem* GoldenEnvironmentSystem = nullptr) // Será definido posteriormente
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            GoldenEnvironmentSystem,
            GetActorLocation(),
            FRotator::ZeroRotator,
            FVector(CurrentRadius / 1000.0f), // Escala baseada no raio
            true,
            true,
            ENCPoolMethod::AutoRelease
        );
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnergyPulse::ApplyGoldenEnvironmentEffects - Applied golden environment effects"));
}

void AAURACRONPCGEnergyPulse::ApplyChaosEnvironmentEffects()
{
    // Implementação robusta para efeitos ambientais do caos

    // Spawn efeitos caóticos no ambiente
    if (UNiagaraSystem* ChaosEnvironmentSystem = nullptr) // Será definido posteriormente
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            ChaosEnvironmentSystem,
            GetActorLocation(),
            FRotator::ZeroRotator,
            FVector(CurrentRadius / 1000.0f), // Escala baseada no raio
            true,
            true,
            ENCPoolMethod::AutoRelease
        );
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnergyPulse::ApplyChaosEnvironmentEffects - Applied chaos environment effects"));
}

void AAURACRONPCGEnergyPulse::ApplyVoidEnvironmentEffects()
{
    // Implementação robusta para efeitos ambientais do vazio

    // Spawn efeitos do vazio no ambiente
    if (UNiagaraSystem* VoidEnvironmentSystem = nullptr) // Será definido posteriormente
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            VoidEnvironmentSystem,
            GetActorLocation(),
            FRotator::ZeroRotator,
            FVector(CurrentRadius / 1000.0f), // Escala baseada no raio
            true,
            true,
            ENCPoolMethod::AutoRelease
        );
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnergyPulse::ApplyVoidEnvironmentEffects - Applied void environment effects"));
}

void AAURACRONPCGEnergyPulse::ApplyGenericEnvironmentEffects()
{
    // Implementação robusta para efeitos ambientais genéricos

    // Spawn efeitos genéricos no ambiente
    if (UNiagaraSystem* GenericEnvironmentSystem = nullptr) // Será definido posteriormente
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            GenericEnvironmentSystem,
            GetActorLocation(),
            FRotator::ZeroRotator,
            FVector(CurrentRadius / 1000.0f), // Escala baseada no raio
            true,
            true,
            ENCPoolMethod::AutoRelease
        );
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnergyPulse::ApplyGenericEnvironmentEffects - Applied generic environment effects"));
}